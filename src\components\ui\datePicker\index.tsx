// utils
import { cn } from '@/lib/utils'

// hooks
import { useState } from 'react'
import { useTranslations } from 'next-intl'

// icons
import { Calendar as CalendarIcon, CircleX } from 'lucide-react'

// ui components
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

// types
import { DatePickerProps, DatePickerValue } from '@/components/ui/datePicker/types'
import { DateRange } from 'react-day-picker'

const formatDateAsDDMMYYYY = (date: Date) => {
  return date.toLocaleDateString('en-US', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  })
}

export function DatePicker({
  label,
  placeholder,
  defaultValue,
  value,
  mode = 'single',
  onChange,
  className,
  onClear,
}: DatePickerProps) {
  const t = useTranslations()

  // states
  const [open, setOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<DatePickerValue>(defaultValue ?? value)

  const formatDisplayDate = (date: DatePickerValue): string => {
    if (!date || (Array.isArray(date) && date.length === 0)) {
      return placeholder || t('label.select_date')
    }

    if (Array.isArray(date)) {
      const visible = date
        .slice(0, 1)
        .map((d) => formatDateAsDDMMYYYY(d))
        .join(', ')
      const extra = date.length > 1 ? ` +${date.length - 1} ${t('label.more')}` : ''
      return `${visible}${extra}`
    }

    if (mode === 'range' && typeof date === 'object' && 'from' in date) {
      const { from, to } = date
      const fromStr = from ? formatDateAsDDMMYYYY(from) : ''
      const toStr = to ? formatDateAsDDMMYYYY(to) : ''
      return toStr ? `${fromStr} → ${toStr}` : fromStr
    }

    return formatDateAsDDMMYYYY(date as Date)
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    setSelectedDate(undefined)
    onChange?.(undefined)
    onClear?.()
  }

  return (
    <div className="flex flex-col gap-3">
      {label && (
        <Label htmlFor="calendar-button" className="px-1">
          {label}
        </Label>
      )}

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id="calendar-button"
            variant="outline"
            className={cn(
              'w-full justify-between font-normal text-muted-foreground capitalize min-w-[250px]',
              selectedDate && 'text-foreground',
              className
            )}
            aria-haspopup="dialog"
            aria-expanded={open}
          >
            {formatDisplayDate(selectedDate)}
            <div className="flex items-center gap-1">
              {selectedDate && (
                <div
                  role="button"
                  tabIndex={0}
                  onClick={handleClear}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      handleClear(e as any)
                    }
                  }}
                  className="flex-1 inline-flex items-center justify-center size-6 rounded-sm hover:bg-muted cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  aria-label={t('label.clear_date')}
                >
                  <CircleX className="size-4 text-muted-foreground" />
                </div>
              )}

              <CalendarIcon className="h-4 w-4 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-auto p-0 overflow-y-auto max-h-[400px]" align="start">
          <Calendar
            mode={mode as any}
            captionLayout={'dropdown' as const}
            firstWeekContainsDate={1 as const}
            weekStartsOn={6 as const} // to make the first day of the week Saturday
            numberOfMonths={mode === 'range' ? 2 : 1}
            selected={selectedDate as DateRange | undefined}
            onSelect={(date: Date | Date[] | DateRange | undefined) => {
              setSelectedDate(date)
              onChange?.(date)
            }}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}

DatePicker.displayName = 'DatePicker'
