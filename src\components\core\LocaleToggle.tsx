'use client'

import { useCallback } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useLocale } from 'next-intl'
import { Button } from '@/components/ui/button'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu'

const LocaleToggle = () => {
  const locale = useLocale()
  const router = useRouter()
  const pathname = usePathname()

  const handleSwitchLocale = useCallback(
    (newLocale: 'ar' | 'en') => {
      // Remove the current locale from the pathname
      const pathnameWithoutLocale = pathname.replace(`/${locale}`, '')

      // Navigate to the new locale
      router.push(`/${newLocale}${pathnameWithoutLocale}`)
    },
    [locale, pathname, router]
  )

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          {locale === 'ar' && 'Ar'}
          {locale === 'en' && 'En'}
          <span className="sr-only">Language toggle</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleSwitchLocale('en')}>EN</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleSwitchLocale('ar')}>AR</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export { LocaleToggle }
