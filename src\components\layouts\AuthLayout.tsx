import Image from 'next/image'
import { PropsWithChildren } from 'react'
import Logo from '/public/icons/logo.svg'
import RegisterBg from '/public/images/register-bg.png'

const AuthLayoutComponent: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <div className="bg-auth">
      <main className="grid grid-cols-1 md:grid-cols-2 bg-white">
        <div className="bg-cover bg-center min-h-screen bg-register">
          <Image alt="register image" height={1080} src={RegisterBg} />
        </div>
        <div className="flex flex-col gap-4 justify-center items-center px-14 py-12">
          <Image alt="logo" height={120} width={130} src={Logo} />
          {children}
        </div>
      </main>
    </div>
  )
}

export default AuthLayoutComponent
