'use client'

import { resetPasswordSchema } from '../schema'
import useResetPassword from './useResetPassword'
import HeaderPage from '../components/HeaderPage'
import { Button } from '@/components/ui/button'
import { FormWrapper } from '@/components/core/FormWrapper'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'

const ResetPasswordModule = () => {
  const {
    t,
    pending,
    onSubmit,
    defaultValues,
    showPassword,
    handleShowPassword,
    showConfirmPassword,
    handleShowConfirmPassword,
  } = useResetPassword()

  return (
    <>
      <HeaderPage title="reset_password" description="enter_new_password" />
      <FormWrapper defaultValues={defaultValues} onSubmit={onSubmit} schema={resetPasswordSchema}>
        <div className="flex flex-col gap-4">
          <FormPasswordInput
            name="new_password"
            label="new_passowrd"
            placeholder="new_passowrd"
            type={showPassword ? 'text' : 'password'}
          />
          <FormPasswordInput
            name="new_password_confirmation"
            label="confirm_new_passowrd"
            placeholder="confirm_new_passowrd"
            type={showConfirmPassword ? 'text' : 'password'}
          />
          <Button type="submit" className="w-full mt-11">
            {t('button.save')}
          </Button>
        </div>
      </FormWrapper>
    </>
  )
}

export default ResetPasswordModule
