import { getCookie } from 'cookies-next';
import { useTranslations } from 'next-intl';
import { redirect } from 'next/navigation';
import { startTransition, useActionState, useEffect, useRef } from 'react';

import { IFormWrapper } from '@/components/ui/Fields/FormWrapper';
import { observer } from '@/utils/observer';
import { resendCodeAction, verifyResetCodeAction } from '../services';

import type { IVerifyResetCode } from '../types';

const defaultValues: IVerifyResetCode = {
  email: '',
  code: '',
};

const useVerifyResetCode = () => {
  const t = useTranslations();
  const email = getCookie('em');
  const formRef = useRef<IFormWrapper>(null);

  const [state, action, pending] = useActionState(verifyResetCodeAction, { status: false });
  const { status, toastMessage } = state;

  const [resendState, resendAction, resendPending] = useActionState(resendCodeAction, { status: false });
  const { status: resendStatus, toastMessage: resendToastMessage } = resendState;

  const onSubmit = async (payload: IVerifyResetCode) => {
    payload.email = email ?? '';
    payload.code = formRef.current?.getValues('code') ?? '';
    startTransition(() => action(payload));
  };

  const handleResendCode = async () => {
    startTransition(() => resendAction());
  };

  useEffect(() => {
    if (toastMessage) {
      if (status) {
        observer.fire('notify', { message: toastMessage, type: 'success' });
        redirect('/auth/reset-password');
      } else {
        observer.fire('notify', { message: toastMessage, type: 'error' });
      }
    }
  }, [state]);

  useEffect(() => {
    if (resendToastMessage) {
      if (resendStatus) {
        observer.fire('notify', { message: resendToastMessage, type: 'success' });
      } else {
        observer.fire('notify', { message: resendToastMessage, type: 'error' });
      }
    }
  }, [resendState]);

  useEffect(() => {
    const handlePopState = () => {
      sessionStorage.removeItem('counter');
    };

    window.addEventListener('popstate', handlePopState);
  }, []);

  const handleEditEmail = () => {
    sessionStorage.removeItem('email_to_reset');
    sessionStorage.removeItem('reset_password_token');
    sessionStorage.removeItem('counter');
    redirect('/auth/forgot-password');
  };

  return {
    t,
    email,
    pending,
    onSubmit,
    defaultValues,
    handleResendCode,
    resendPending,
    formRef,
    handleEditEmail,
  };
};

export default useVerifyResetCode;
