@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Color palette example 

  --color-error-100: ;
  --color-error-200: ;
  --color-error-300: ;
  --color-error-400: ;
  --color-error-500: ;
  --color-error-600: ;
  --color-error-700: ;
  --color-error-800: ;
  --color-error-900: ; 
*/

@layer base {
  html {
    /* base gradient */
    --color-start-gradient: #DEDODE;
    --color-end-gradient: #FFFFFF;

    /* primary colors */
    --color-primary-01: #BC2933;
    --color-primary-02: #444395;
    --color-primary-03: #4B5563;

    --color-gray-01: #A7A9AC;
    --color-gray-02: #DCDDDE;
    --color-gray-03: #F9F9F9;

    /* primary background colors */
    --color-primary-bg: #F5F4F5;
    --color-secondary-bg: #D9D9D9;
    --color-primary-02-bg: #E6E6EE;
    --color-primary-03-bg: #E6E7E9;

    /* action background colors */
    --color-action-01-bg: #F6F1E0;
    --color-action-02-bg: #E0F1F4;
    --color-action-03-bg: #E2F3E8;
    --color-action-04-bg: #E3E9F6;
    --color-action-05-bg: #EEE4F6;
    --color-action-06-bg: #F5E3EB;

    /* action colors */
    --color-action-01: #EAB308;
    --color-action-02: #06B6D4;
    --color-action-03: #22C55E;
    --color-action-04: #2563EB;
    --color-action-05: #9333EA;
    --color-action-06: #DB2777;

    /* button gradient */
    --color-btn-gradient-01: #4A4190;
    --color-btn-gradient-02: #7D3766;
    --color-btn-gradient-03: #A32F47;
    --color-btn-gradient-04: #BC2933;
    --color-btn-gradient-05: #913355;

    /* card shadow */
    --color-card-shadow-color: #444395;

    --color-primary-50: #f9f4ea;
    --color-primary-100: #eeddbd;
    --color-primary-200: #e5cc9d;
    --color-primary-300: #d9b570;
    --color-primary-400: #d2a655;
    --color-primary-500: #c7902a;
    --color-primary-600: #b58326;
    --color-primary-700: #8d661e;
    --color-primary-800: #6d4f17;
    --color-primary-900: #543c12;

    --color-gray-50: #f7f8f9;
    --color-gray-100: #eef0f3;
    --color-gray-200: #ffffff;
    --color-gray-300: #bbc3cf;
    --color-gray-400: #8896ab;
    --color-gray-500: #556987;
    --color-gray-600: #4d5f7a;
    --color-gray-700: #404f65;
    --color-gray-800: #333f51;
    --color-gray-900: #2a3342;

    --color-success-100: #dcfce7;
    --color-success-200: #bbf7d0;
    --color-success-300: #86efac;
    --color-success-400: #4ade80;
    --color-success-500: #22c55e;
    --color-success-600: #16a34a;
    --color-success-700: #15803d;
    --color-success-800: #166534;
    --color-success-900: #14532d;

    --color-error-100: #fdeeec;
    --color-error-200: #fbd6d0;
    --color-error-300: #f9bdb4;
    --color-error-400: #f48b7c;
    --color-error-500: #ef5944;
    --color-error-600: #d7503d;
    --color-error-700: #b34333;
    --color-error-800: #752c21;
    --color-error-900: #752c21;

    --color-warning-100: #fef5e7;
    --color-warning-200: #fde7c2;
    --color-warning-300: #fbd89d;
    --color-warning-400: #f8bb54;
    --color-warning-500: #f59e0b;
    --color-warning-600: #dd8e0a;
    --color-warning-700: #b87708;
    --color-warning-800: #935f07;
    --color-warning-900: #784d05;

    --color-info-100: #cee0fd;
    --color-info-200: #cee0fd;
    --color-info-300: #b1cdfb;
    --color-info-400: #76a8f9;
    --color-info-500: #3b82f6;
    --color-info-600: #3575dd;
    --color-info-700: #2c62b9;
    --color-info-800: #234e94;
    --color-info-900: #1d4079;

    --color-secondary: #4a425d;

    --color-label: var(--color-gray-800);
    --color-disabled: var(--color-gray-300);
    --color-typography: var(--color-gray-500);
    --color-placeholder: var(--color-gray-300);

    --color-bg-card: #f8f8f8;
    --color-bg-disabled: #f8f8f8;
    --color-bg-paper: var(--color-gray-200);
    --color-error: #e7000b;
  }

  html[class="dark"] {
    --color-label: var(--color-gray-200);
    --color-typography: var(--color-gray-200);
    --color-placeholder: var(--color-gray-500);

    --color-bg-card: #3d3d3d;
    --color-bg-paper: #282828;
    --color-bg-disabled: var(--color-gray-400);
  }

  .container {
    padding: 1.5rem;
  }
}

body {
  background-color: #f8f8f8;
}

.rdp-root {
  --rdp-accent-color: var(--color-primary-500) !important;
  --rdp-accent-background-color: var(--color-primary-500) !important;
}

.rdp-day.rdp-today.rdp-selected:not(.rdp-range_middle) {
  @apply text-white;
}

.rdp-day.rdp-today.rdp-selected.rdp-range_middle {
  @apply text-black}

html[class="dark"] {
  body {
    background-color: #131313;
  }
}

/* width */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  border-radius: 7px;
  overflow: hidden;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--color-gray-500);
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}

/*
   Animation example, for spinners
*/
.animate-spin {
  -moz-animation: spin 1.5s infinite linear;
  -o-animation: spin 1.5s infinite linear;
  -webkit-animation: spin 1.5s infinite linear;
  animation: spin 1.5s infinite linear;
  display: inline-block;
}
@-moz-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-webkit-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-o-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@-ms-keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
@keyframes spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}


@layer components {
  .base_gradient {
    @apply bg-gradient-to-br from-start_gradient to-end_gradient min-h-screen bg-fixed;
  }
  
  .main_btn_gradient {
    background: linear-gradient(to right, 
      var(--btn-gradient-01), 
      var(--btn-gradient-02), 
      var(--btn-gradient-03), 
      var(--btn-gradient-04), 
      var(--btn-gradient-05)
    );
  }
  
  .basic_card_shadow {
    @apply shadow-[0_20px_25px_-5px_rgba(68,67,149,0.1),0_10px_10px_-5px_rgba(68,67,149,0.04)];
  }

  .border_gradient {
    background: linear-gradient(to right, 
      var(--color-start-gradient), 
      var(--color-gray-01), 
      var(--color-start-gradient)
    );
  }
}

.bg-register {
  background-image: url('/images/register-bg.png');
}