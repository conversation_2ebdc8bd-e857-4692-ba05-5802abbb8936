import { actionService } from '@/services'
import { IRegister } from '@/types'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

const useRegister = () => {
  const t = useTranslations()

  const handleSubmit = async (payload: IRegister) => {
    try {
      const result = await actionService(
        {
          path: 'auth/register',
          method: 'post',
        },
        null,
        payload
      )

      console.log(result)

      if (result.status) {
        toast.success('تم التسجيل بنجاح')
      }
    } catch (error) {
      console.error('خطأ في التسجيل:', error)
      toast.error('خطأ في التسجيل')
    }
  }
  return {
    t,
    handleSubmit,
  }
}

export default useRegister
