'use client'

import { FieldError, useFormContext } from 'react-hook-form'

import { verifyResetCodeSchema } from '../schema'
import Counter from './components/Counter'
import useVerifyResetCode from './useVerifyResetCode'
import HeaderPage from '../components/HeaderPage'
import { Button } from '@/components/ui/button'
import { FormWrapper } from '@/components/core/FormWrapper'
import { Edit } from 'lucide-react'

const VerifyResetCodeModule = () => {
  const { email, defaultValues, onSubmit, t, pending, handleResendCode, resendPending, formRef, handleEditEmail } =
    useVerifyResetCode()

  return (
    <>
      <HeaderPage title="verification_code" />
      <div className="text-center text-gray-500 font-medium mb-8">
        {t.rich('auth.enter_code_email', {
          email: () => (
            <div className="flex items-center justify-center gap-1">
              <a href="/guidelines">{email}</a>
              <Button onClick={handleEditEmail}>
                <Edit size="20" color="#197BBD" />
              </Button>
            </div>
          ),
        })}
      </div>
      <FormWrapper ref={formRef} defaultValues={defaultValues} onSubmit={onSubmit} schema={verifyResetCodeSchema}>
        <div className="flex flex-col gap-4 items-center">
          <Counter />
          <InputOTPComponent />

          <Button type="submit" className="w-full mt-6">
            {t('button.verify')}
          </Button>
          <Button type="button" onClick={handleResendCode} className="w-full mt-2">
            {t('button.resend_code')}
          </Button>
        </div>
      </FormWrapper>
    </>
  )
}

export default VerifyResetCodeModule

const InputOTPComponent = () => {
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext()

  return (
    <>
      <InputOTP maxLength={6} value={watch('code')} onChange={(value) => setValue('code', value)}>
        <InputOTPGroup>
          <InputOTPSlot index={0} />
          <InputOTPSlot index={1} />
          <InputOTPSlot index={3} />
          <InputOTPSlot index={4} />
        </InputOTPGroup>
      </InputOTP>
      {errors['code'] && <ErrorMessage error={errors['code'] as FieldError} />}
    </>
  )
}
