// import LocaleSwitcher from '@/components/shared/locale-switcher';
import { LocaleToggle } from '@/components/core/LocaleToggle'
import { FaqsItem } from '@/components/shared/faqsItem'
import ThemeSwitch from '@/components/shared/ThemeSwitch'
import { apiService } from '@/services'
import { IFaqs } from '@/types/faqs'

// import { getTranslations } from 'next-intl/server';

export default async function Home() {
  const home = await apiService({
    path: 'home',
  })

  console.log(home)
  return (
    <main className="flex min-h-screen flex-col items-center justify-start gap-10 px-14">
      {/* <LocaleSwitcher /> */}
      <ThemeSwitch />
      <LocaleToggle />

      <section className="flex flex-col gap-2 items-center justify-center min-w-full p-4 mb-20 ">
        {home?.data?.faqs.map((item: IFaqs) => <FaqsItem key={item.question} {...item} />)}
      </section>
    </main>
  )
}
