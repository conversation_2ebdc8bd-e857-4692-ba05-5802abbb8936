'use server'

import { ActionServiceReturn } from '@/types'
import { apiService, ApiServiceProps } from './apiService'

export type ActionServiceProps = ApiServiceProps

export async function actionService<T>(props: ActionServiceProps, _: any, body: any): Promise<ActionServiceReturn<T>> {
  const res = await apiService({ body, ...props })
  console.log('aaaaaa', res)
  return {
    status: true,
    message: res.text,
  }
}
