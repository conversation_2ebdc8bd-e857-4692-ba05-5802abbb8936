'use server';

import { cookies } from 'next/headers';

import { env } from '@/config/environment';
import { i18n } from '@/i18n-config';

// In this example the locale is read from a cookie. You could alternatively
// also read it from a database, backend service, or any other source.
const COOKIE_NAME = env.LANGUAGE;

export async function getUserLocale() {
  return (await cookies()).get(COOKIE_NAME)?.value || i18n.defaultLocale;
}

export async function setLocaleCookie(locale: string) {
  (await cookies()).set(COOKIE_NAME, locale);
}
