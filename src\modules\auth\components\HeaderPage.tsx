import { useTranslations } from 'next-intl'

export interface IHeaderPage {
  title: string
  description?: string
}

const HeaderPage = ({ title, description }: IHeaderPage) => {
  const t = useTranslations()
  return (
    <>
      <h2 className="text-center text-4xl font-bold text-primary-01 mb-2 mt-12">{t(`auth.${title}`)}</h2>
      {description && <p className="text-center text-[22px] text-primary-03"> {t(`auth.${description}`)}</p>}
    </>
  )
}

export default HeaderPage
