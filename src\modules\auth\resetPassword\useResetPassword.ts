import { getCookie } from 'cookies-next';
import { useTranslations } from 'next-intl';
import { redirect } from 'next/navigation';
import { startTransition, useActionState, useEffect, useState } from 'react';

import { observer } from '@/utils/observer';
import { resetPasswordAction } from '../services';
import { IResetPassword } from '../types';

const defaultValues: IResetPassword = {
  email: '',
  token: '',
  new_password: '',
  new_password_confirmation: '',
};

const useResetPassword = () => {
  const t = useTranslations();
  const token = getCookie('rt');
  const email = getCookie('em');

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [state, action, pending] = useActionState(resetPasswordAction, { status: false });
  const { status, toastMessage } = state;

  const onSubmit = async (payload: IResetPassword) => {
    payload.email = email ?? '';
    payload.token = token ?? '';
    startTransition(() => action(payload));
  };

  const handleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  useEffect(() => {
    if (toastMessage) {
      if (status) {
        observer.fire('notify', { message: toastMessage, type: 'success' });
        redirect('/auth/login');
      } else {
        observer.fire('notify', { message: toastMessage, type: 'error' });
      }
    }
  }, [state]);

  return {
    t,
    pending,
    onSubmit,
    defaultValues,
    showPassword,
    handleShowPassword,
    showConfirmPassword,
    handleShowConfirmPassword,
  };
};

export default useResetPassword;
