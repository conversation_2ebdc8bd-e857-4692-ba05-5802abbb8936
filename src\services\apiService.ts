'use server'

import { signOut } from 'next-auth/react'
import { getServerAuthSession } from '@/config/auth'
import { env } from '@/config/environment'
import { getLocale } from 'next-intl/server'

export interface ApiServiceProps extends RequestInit {
  path: string
  searchParams?: Record<string, any>
}

export async function apiService({ path, searchParams, ...props }: ApiServiceProps) {
  try {
    const locale = await getLocale()
    const user = await getServerAuthSession()
    
    // Validate BASE_API environment variable
    if (!env.BASE_API) {
      console.error('BASE_API environment variable is not set')
      
    }
    
    const headers = {
      ...props.headers,
      'Accept-Language': locale,
      'Accept': 'application/json',
      ...(user?.user.token && { Authorization: `Bearer ${user?.user.token}` }),
    }

    const BASE_URL = env.BASE_API
    const urlSearchParams = new URLSearchParams(searchParams)
    const url = `${BASE_URL}${path}${!!urlSearchParams.size ? `?${new URLSearchParams(searchParams)}` : ''}`

    const res = await fetch(url, {
      ...props,
      method: props.method || 'GET',
      headers,
      next: { tags: [path, urlSearchParams.toString() || ''], ...props.next },
    })

    if (res.ok) {
      const jsonRes = await res.json()
      console.log('req success log:', path, res.status, jsonRes)

      return jsonRes
    } else {
      console.error('❌ Request failed:', path, res.status, res.statusText)
      
      // Try to get error details from response
      let errorDetails = ''
      try {
        const errorRes = await res.text()
        errorDetails = errorRes
      } catch (e) {
        errorDetails = 'Unable to read error response'
      }
      
      console.error('❌ Error details:', errorDetails)
      
      if (res.status === 401) {
        return signOut({ callbackUrl: `/auth/login`, redirect: true })
      } else {
        // new Error("Not Found");
      }
    }
  } catch (e) {
    console.error('🚀 ~ apiService ~ error when getting data:', path, e)
   
  }
}
