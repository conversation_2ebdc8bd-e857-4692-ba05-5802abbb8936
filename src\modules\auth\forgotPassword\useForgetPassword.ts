'use client'

import { useTranslations } from 'next-intl'
import { IForgetPassword } from '@/types'
import { actionService } from '@/services'
import { toast } from 'sonner'

const defaultValues: IForgetPassword = {
  email: '',
}

const useForgetPassword = () => {
  const t = useTranslations()

  const handleSubmit = async (payload: IForgetPassword) => {
    try {
      const result = await actionService(
        {
          path: 'auth/forgot-password',
          method: 'post',
        },
        null,
        payload
      )

      console.log(result)

      if (result.status) {
        toast.success('تم التسجيل بنجاح')
      }
    } catch (error) {
      console.error('خطأ في التسجيل:', error)
      toast.error('خطأ في التسجيل')
    }
  }

  return {
    t,
    handleSubmit,
    defaultValues,
  }
}

export default useForgetPassword
