'use client'

import { FormWrapper } from '@/components/core/FormWrapper'
import { FormInput } from '@/components/form'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { loginSchema } from '../schema'
import useLogin from './useLogin'
import HeaderPage from '../components/HeaderPage'

const defaultValues = {
  email: '',
  password: '',
}

const Login = () => {
  const { t, handleSubmit } = useLogin()
  return (
    <>
      <HeaderPage title="login_to_account" description="welcome_back" />
      <FormWrapper schema={loginSchema} defaultValues={defaultValues} onSubmit={handleSubmit}>
        <FormInput name="email" type="email" label={t('label.email')} placeholder={t('label.email')} />
        <FormPasswordInput name="password" label={t('label.password')} placeholder={t('label.password')} />
        <Link className="text-primary-02 font-bold block w-full text-end" href={'#'}>
          {t('auth.forgot_password')}
        </Link>
        <div className="text-center">
          <Button type="submit">{t('auth.login')}</Button>
          <p className="text-primary-02 font-bold">
            {t('auth.no_account_yet')}
            <Link className="text-primary-01" href={'#'}>
              {t('auth.register_now')}
            </Link>
          </p>
        </div>
      </FormWrapper>
    </>
  )
}

export default Login
