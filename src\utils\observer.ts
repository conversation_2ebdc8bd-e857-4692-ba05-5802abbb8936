class Observable {
  static instance: Observable;
  private handlers: { [key: string]: (...args: any[]) => void } = {};

  constructor() {
    if (Observable.instance) {
      return Observable.instance;
    }
    Observable.instance = this;
    Object.freeze(this);
  }

  subscribe<T extends string>(fnName: T, callback: (...args: any[]) => void): void {
    if (!fnName || typeof callback !== 'function') {
      throw new Error('Invalid parameters passed to subscribe');
    }
    this.handlers[fnName] = callback;
  }

  unsubscribe(fnName: string): void {
    if (fnName in this.handlers) {
      delete this.handlers[fnName];
    }
  }

  fire<T extends string, K>(fnName: T, data?: K): void {
    if (this.handlers[fnName]) {
      this.handlers[fnName](data);
    }
  }

  isExist(handlerKey: string): boolean {
    return !!this.handlers[handler<PERSON><PERSON>];
  }
}

export const observer = new Observable();
