'use client'

import Link from 'next/link'

import { forgotPasswordSchema } from '../schema'
import useForgetPassword from './useForgetPassword'
import { FormWrapper } from '@/components/core/FormWrapper'
import { FormInput } from '@/components/form'
import { But<PERSON> } from '@/components/ui/button'
import HeaderPage from '../components/HeaderPage'

const ForgetPassword = () => {
  const { defaultValues, t, handleSubmit } = useForgetPassword()

  return (
    <>
      <HeaderPage title="forgot_password" description="enter_email_reset" />
      <FormWrapper defaultValues={defaultValues} onSubmit={handleSubmit} schema={forgotPasswordSchema}>
        <div className="flex flex-col">
          <FormInput name="email" type="email" label={t('label.email')} placeholder={t('label.email')} />
          <Button type="submit">{t('button.send')}</Button>
          <Link className="text-primary-02 font-bold block w-full text-center" href={'/auth/login'}>
            {t('button.back_to_login')}
          </Link>
        </div>
      </FormWrapper>
    </>
  )
}

export default ForgetPassword
